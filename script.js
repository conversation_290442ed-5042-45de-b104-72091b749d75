document.addEventListener('DOMContentLoaded', () => {
    // 获取Canvas元素和上下文
    const canvas = document.getElementById('gameCanvas');
    const ctx = canvas.getContext('2d');

    // 游戏配置
    const gridSize = 20;
    const gridWidth = canvas.width / gridSize;
    const gridHeight = canvas.height / gridSize;

    // 零食类型定义
    const foodTypes = [
        { type: 'apple', color: '#FF0000', points: l10, growthAmount: 1, probability: 0.4, shape: 'circle' },
        { type: 'banana', color: '#FFD700', points: 15, growthAmount: 1, probability: 0.2, shape: 'circle' },
        { type: 'cherry', color: '#FF1493', points: 20, growthAmount: 1, probability: 0.15, shape: 'circle' },
        { type: 'donut', color: '#FFC0CB', points: 30, growthAmount: 2, probability: 0.1, shape: 'square' },
        { type: 'egg', color: '#FFFFFF', points: 40, growthAmount: 2, probability: 0.08, shape: 'circle' },
        { type: 'fries', color: '#FFA500', points: 50, growthAmount: 2, probability: 0.05, shape: 'square' },
        { type: 'burger', color: '#8B4513', points: 100, growthAmount: 3, probability: 0.02, shape: 'square' }
    ];

    // 游戏状态变量
    let snake = [];
    let foods = [];
    const maxFoods = 5;
    let direction = 'right';
    let nextDirection = 'right';
    let score = 0;
    let gameSpeed = 150;
    let gameInterval;
    let foodGenerationInterval;
    let gameRunning = false;

    // 获取DOM元素
    const scoreElement = document.getElementById('score');
    const startButton = document.getElementById('startButton');

    // 新增速度调节按钮和显示
    const speedUpButton = document.createElement('button');
    speedUpButton.textContent = '加速';
    speedUpButton.id = 'speedUpButton';
    speedUpButton.style.position = 'absolute';
    speedUpButton.style.top = '10px';
    speedUpButton.style.right = '10px';

    const speedDownButton = document.createElement('button');
    speedDownButton.textContent = '减速';
    speedDownButton.id = 'speedDownButton';
    speedDownButton.style.position = 'absolute';
    speedDownButton.style.top = '40px';
    speedDownButton.style.right = '10px';

    const speedDisplay = document.createElement('div');
    speedDisplay.textContent = `速度: ${200 - gameSpeed}`;
    speedDisplay.id = 'speedDisplay';
    speedDisplay.style.position = 'absolute';
    speedDisplay.style.top = '70px';
    speedDisplay.style.right = '10px';
    speedDisplay.style.color = 'white';

    document.body.appendChild(speedUpButton);
    document.body.appendChild(speedDownButton);
    document.body.appendChild(speedDisplay);
    // 速度调节功能
    speedUpButton.addEventListener('click', () => {
        if (gameSpeed > 60) {
                gameSpeed -= 10;
            updateGameSpeed();
            }
    });

    speedDownButton.addEventListener('click', () => {
        if (gameSpeed < 200) {
            gameSpeed += 10;
            updateGameSpeed();
        }
    });

    // 新增：统一更新速度的函数
    function updateGameSpeed() {
            clearInterval(gameInterval);
        gameInterval = setInterval(gameLoop, gameSpeed);
        speedDisplay.textContent = `速度: ${getSpeedLevel(gameSpeed)}`;
    }

    // 新增：将数值转换为速度等级
    function getSpeedLevel(speed) {
        if (speed > 150) return '慢速';
        if (speed > 100) return '中速';
        if (speed > 60) return '快速';
        return '极速';
            }

    // 初始化游戏
    function initGame() {
        // 初始化蛇（3个单位长，初始位置在中间）
        snake = [
            {x: Math.floor(gridWidth / 2), y: Math.floor(gridHeight / 2)},
            {x: Math.floor(gridWidth / 2) - 1, y: Math.floor(gridHeight / 2)},
            {x: Math.floor(gridWidth / 2) - 2, y: Math.floor(gridHeight / 2)}
        ];

        // 重置方向和分数
        direction = 'right';
        nextDirection = 'right';
        score = 0;
        scoreElement.textContent = score;

        // 清空食物数组
        foods = [];

        // 生成初始食物
        for (let i = 0; i < 3; i++) { // 开始时生成3个食物
            generateFood();
        }

        // 如果游戏正在运行，清除现有间隔
        if (gameRunning) {
            clearInterval(gameInterval);
            clearInterval(foodGenerationInterval);
        }
        // 开始游戏循环
        gameRunning = true;
        gameInterval = setInterval(gameLoop, gameSpeed);

        // 定期生成新食物（每5秒尝试生成一次，如果场上食物少于最大值）
        foodGenerationInterval = setInterval(() => {
            if (foods.length < maxFoods) {
                generateFood();
            }
        }, 5000);

        // 更新按钮文本
        startButton.textContent = '重新开始';
    }

    // 选择食物类型
    function selectFoodType() {
        // 使用轮盘赌方法根据概率选择食物类型
        const rand = Math.random();
        let cumulativeProbability = 0;

        for (const foodType of foodTypes) {
            cumulativeProbability += foodType.probability;
            if (rand <= cumulativeProbability) {
                return foodType;
            }
        }

        // 默认返回苹果
        return foodTypes[0];
    }

    // 生成食物
    function generateFood() {
        // 如果已经达到最大食物数量，不再生成
        if (foods.length >= maxFoods) {
            return;
        }

        // 随机位置生成食物，确保不与蛇身和其他食物重叠
        let newFood;
        let validPosition;
        let attempts = 0;
        const maxAttempts = 50; // 最大尝试次数，避免无限循环
        do {
            validPosition = true;
            newFood = {
                x: Math.floor(Math.random() * gridWidth),
                y: Math.floor(Math.random() * gridHeight),
                // 选择食物类型
                ...selectFoodType(),
                // 添加一个唯一ID
                id: Date.now() + Math.random()
            };

            // 检查是否与蛇身重叠
            for (let i = 0; i < snake.length; i++) {
                if (snake[i].x === newFood.x && snake[i].y === newFood.y) {
                    validPosition = false;
                    break;
                }
            }

            // 检查是否与其他食物重叠
            if (validPosition) {
                for (let i = 0; i < foods.length; i++) {
                    if (foods[i].x === newFood.x && foods[i].y === newFood.y) {
                        validPosition = false;
                        break;
                    }
                }
            }

            attempts++;
            if (attempts >= maxAttempts) {
                // 如果多次尝试都找不到合适位置，放弃本次生成
                return;
            }
        } while (!validPosition);

        foods.push(newFood);
    }

    // 游戏主循环
    function gameLoop() {
        update();
        draw();
    }

    // 更新游戏状态
    function update() {
        // 更新方向
        direction = nextDirection;

        // 计算蛇头的新位置
        const head = {x: snake[0].x, y: snake[0].y};

        switch (direction) {
            case 'up':
                head.y -= 1;
                break;
            case 'down':
                head.y += 1;
                break;
            case 'left':
                head.x -= 1;
                break;
            case 'right':
                head.x += 1;
                break;
        }

        // 边界检查 - 游戏结束条件1：撞墙
        if (head.x < 0 || head.x >= gridWidth || head.y < 0 || head.y >= gridHeight) {
            gameOver();
            return;
        }

        // 自身碰撞检查 - 游戏结束条件2：撞到自己
        for (let i = 0; i < snake.length; i++) {
            if (head.x === snake[i].x && head.y === snake[i].y) {
                gameOver();
                return;
            }
        }

        // 在数组开头添加新的头部
        snake.unshift(head);

        // 检查是否吃到食物
        let foodEaten = false;
        let eatenFoodIndex = -1;
        let eatenFood = null;

        // 检查蛇头是否与任何一个食物重叠
        for (let i = 0; i < foods.length; i++) {
            if (head.x === foods[i].x && head.y === foods[i].y) {
                eatenFoodIndex = i;
                eatenFood = foods[i];
                foodEaten = true;
                break;
            }
        }

        if (foodEaten && eatenFood) {
            // 增加分数
            score += eatenFood.points;
            scoreElement.textContent = score;

            // 显示得分动画
            showPointsAnimation(eatenFood);

            // 每增加100分加速一次
            if (score % 100 === 0 && gameSpeed > 60) {
                gameSpeed -= 10;
                clearInterval(gameInterval);
                gameInterval = setInterval(gameLoop, gameSpeed);
            }

            // 从数组中移除被吃掉的食物
            foods.splice(eatenFoodIndex, 1);

            // 生成新食物，有1/3的概率生成
            if (Math.random() < 0.33 && foods.length < maxFoods) {
                generateFood();
            }

            // 根据食物类型决定蛇增长的长度
            // 注意：我们已经添加了一个头部，所以这里只需要阻止删除尾部即可
            // 食物的growthAmount减1，因为已经增加了一个头部
            for (let i = 0; i < eatenFood.growthAmount - 1; i++) {
                // 在尾部复制一个节点，模拟增长
                const tail = snake[snake.length - 1];
                snake.push({x: tail.x, y: tail.y});
            }
        } else {
            // 如果没有吃到食物，移除尾部（保持蛇的长度）
            snake.pop();
        }

        // 确保始终有最少数量的食物在场上
        if (foods.length < 2) {
            generateFood();
        }
    }

    // 显示得分动画
    function showPointsAnimation(food) {
        // 创建一个临时div元素来显示得分
        const pointsDiv = document.createElement('div');
        pointsDiv.className = 'points-animation';
        pointsDiv.textContent = `+${food.points}`;
        pointsDiv.style.position = 'absolute';
        pointsDiv.style.color = food.color;
        pointsDiv.style.fontWeight = 'bold';
        pointsDiv.style.fontSize = '20px';

        // 计算位置（相对于canvas）
        const canvasRect = canvas.getBoundingClientRect();
        pointsDiv.style.left = `${canvasRect.left + food.x * gridSize}px`;
        pointsDiv.style.top = `${canvasRect.top + food.y * gridSize}px`;

        // 添加到页面
        document.body.appendChild(pointsDiv);

        // 添加动画效果
        pointsDiv.style.transition = 'all 1s ease-out';
        setTimeout(() => {
            pointsDiv.style.transform = 'translateY(-30px)';
            pointsDiv.style.opacity = '0';
        }, 50);

        // 移除元素
        setTimeout(() => {
            document.body.removeChild(pointsDiv);
        }, 1000);
    }

    // 绘制游戏
    function draw() {
        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 绘制蛇
        snake.forEach((segment, index) => {
            // 蛇头使用不同颜色
            if (index === 0) {
                ctx.fillStyle = '#006400'; // 深绿色蛇头
            } else {
                ctx.fillStyle = '#32CD32'; // 浅绿色蛇身
            }

            ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 1, gridSize - 1);
        });

        // 绘制所有食物
        foods.forEach(food => {
            ctx.fillStyle = food.color;

            if (food.shape === 'circle') {
                // 绘制圆形食物
                ctx.beginPath();
                ctx.arc(
                    food.x * gridSize + gridSize / 2,
                    food.y * gridSize + gridSize / 2,
                    gridSize / 2 - 1,
                    0,
                    Math.PI * 2
                );
                ctx.fill();
            } else {
                // 绘制方形食物
                ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize - 1, gridSize - 1);
            }
        });

        // 绘制网格（可选）
        if (false) { // 设置为true可以显示网格线
            ctx.strokeStyle = '#CCCCCC';
            ctx.lineWidth = 0.5;

            // 垂直线
            for (let x = 0; x <= canvas.width; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }

            // 水平线
            for (let y = 0; y <= canvas.height; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
        }
    }

    // 游戏结束
    function gameOver() {
        clearInterval(gameInterval);
        clearInterval(foodGenerationInterval);
        gameRunning = false;

        // 绘制游戏结束文本
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.font = '30px Arial';
        ctx.fillStyle = 'white';
        ctx.textAlign = 'center';
        ctx.fillText('游戏结束!', canvas.width / 2, canvas.height / 2 - 30);

        ctx.font = '20px Arial';
        ctx.fillText(`最终得分: ${score}`, canvas.width / 2, canvas.height / 2 + 10);
        ctx.fillText('点击"重新开始"按钮再玩一次', canvas.width / 2, canvas.height / 2 + 50);

        startButton.textContent = '重新开始';
    }

    // 键盘控制
    document.addEventListener('keydown', (event) => {
        if (!gameRunning) return;

        // 防止蛇直接掉头（这会导致游戏结束，因为会撞到自己）
        switch (event.key) {
            case 'ArrowUp':
            case 'w':
            case 'W':
                if (direction !== 'down') nextDirection = 'up';
                break;
            case 'ArrowDown':
            case 's':
            case 'S':
                if (direction !== 'up') nextDirection = 'down';
                break;
            case 'ArrowLeft':
            case 'a':
            case 'A':
                if (direction !== 'right') nextDirection = 'left';
                break;
            case 'ArrowRight':
            case 'd':
            case 'D':
                if (direction !== 'left') nextDirection = 'right';
                break;
        }

        // 防止方向键滚动页面
        if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
            event.preventDefault();
        }

        // 新增：键盘快捷键支持
        if (event.key === '+' && gameSpeed > 60) {
            gameSpeed -= 10;
            updateGameSpeed();
        } else if (event.key === '-' && gameSpeed < 200) {
            gameSpeed += 10;
            updateGameSpeed();
        }
    });

    // 添加移动端触摸控制（可选）
    let touchStartX = 0;
    let touchStartY = 0;

    canvas.addEventListener('touchstart', (e) => {
        if (!gameRunning) return;

        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
        e.preventDefault();
    });

    canvas.addEventListener('touchmove', (e) => {
        if (!gameRunning) return;
        e.preventDefault();
    });

    canvas.addEventListener('touchend', (e) => {
        if (!gameRunning) return;

        const touchEndX = e.changedTouches[0].clientX;
        const touchEndY = e.changedTouches[0].clientY;

        const deltaX = touchEndX - touchStartX;
        const deltaY = touchEndY - touchStartY;

        // 确定主要的滑动方向
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // 水平滑动
            if (deltaX > 0 && direction !== 'left') {
                nextDirection = 'right';
            } else if (deltaX < 0 && direction !== 'right') {
                nextDirection = 'left';
            }
        } else {
            // 垂直滑动
            if (deltaY > 0 && direction !== 'up') {
                nextDirection = 'down';
            } else if (deltaY < 0 && direction !== 'down') {
                nextDirection = 'up';
            }
        }

        e.preventDefault();
    });

    // 开始/重新开始按钮事件监听器
    startButton.addEventListener('click', initGame);

    // 初始绘制游戏界面
    draw();
});
