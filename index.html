<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多零食贪吃蛇游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <h1>多零食贪吃蛇游戏</h1>
        <canvas id="gameCanvas" width="400" height="400"></canvas>
        <div class="game-controls">
    <button id="startButton" style="background-color: black; color: white;">开始游戏</button>
            <div class="score-container">分数: <span id="score">0</span></div>
            <button id="toggleGuideButton">显示食物指南</button>
            <div class="instructions">
                <p>使用键盘方向键控制蛇的移动</p>
                <p>↑ ↓ ← → 或 W A S D</p>
                <p><strong>游戏特色:</strong> 场上同时存在多种零食，选择高价值的零食可获得更多分数！</p>
            </div>
            <div class="food-guide" id="foodGuide">
                <h3>零食指南</h3>
                <div class="food-guide-item">
                    <div class="food-color" style="background-color: #FF0000;"></div>
                    <span>苹果: 10分, 增长1格</span>
                </div>
                <div class="food-guide-item">
                    <div class="food-color" style="background-color: #FFD700;"></div>
                    <span>香蕉: 15分, 增长1格</span>
                </div>
                <div class="food-guide-item">
                    <div class="food-color" style="background-color: #FF1493;"></div>
                    <span>樱桃: 20分, 增长1格</span>
                </div>
                <div class="food-guide-item">
                    <div class="food-color" style="background-color: #FFC0CB;"></div>
                    <span>甜甜圈: 30分, 增长2格</span>
                </div>
                <div class="food-guide-item">
                    <div class="food-color" style="background-color: #FFFFFF; border: 1px solid #ccc;"></div>
                    <span>鸡蛋: 40分, 增长2格</span>
                </div>
                <div class="food-guide-item">
                    <div class="food-color" style="background-color: #FFA500;"></div>
                    <span>薯条: 50分, 增长2格</span>
                </div>
                <div class="food-guide-item">
                    <div class="food-color" style="background-color: #8B4513;"></div>
                    <span>汉堡: 70分, 增长3格</span>
                </div>
                <p class="food-guide-note">* 游戏中会同时出现多种零食，选择性地吃取高分值的零食可以获得更高分数！</p>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
    <script>
        // 食物指南显示/隐藏
        document.getElementById('toggleGuideButton').addEventListener('click', function() {
            const guide = document.getElementById('foodGuide');
            if (guide.style.display === 'block') {
                guide.style.display = 'none';
                this.textContent = '显示食物指南';
            } else {
                guide.style.display = 'block';
                this.textContent = '隐藏食物指南';
            }
        });
    </script>
</body>
</html>
