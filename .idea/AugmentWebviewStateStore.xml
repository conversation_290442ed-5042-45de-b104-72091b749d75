<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;a1f2168b-43eb-439f-a81a-2fc3512fb805&quot;,&quot;conversations&quot;:{&quot;9ed8dc7f-82ec-4acd-bf15-6121508902e9&quot;:{&quot;id&quot;:&quot;9ed8dc7f-82ec-4acd-bf15-6121508902e9&quot;,&quot;createdAtIso&quot;:&quot;2025-06-19T08:46:37.855Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-19T08:46:37.855Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;4dbc60c6-62aa-4acd-8a60-d5e79f9e7959&quot;},&quot;a1f2168b-43eb-439f-a81a-2fc3512fb805&quot;:{&quot;id&quot;:&quot;a1f2168b-43eb-439f-a81a-2fc3512fb805&quot;,&quot;createdAtIso&quot;:&quot;2025-06-19T08:46:37.942Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-19T08:46:37.942Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;4e1292ad-2f85-4822-ad20-4c8d9e6c4210&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>