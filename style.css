* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.game-container {
    text-align: center;
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
}

h1 {
    color: #333;
    margin-bottom: 20px;
}

canvas {
    background-color: #c1e1c5;
    border: 2px solid #333;
    display: block;
    margin: 0 auto;
}

.game-controls {
    margin-top: 20px;
}

.score-container {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 10px;
}

button {
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    margin: 10px 0;
    transition: background-color 0.3s;
}

/* 开始/重新开始按钮 */
#startButton {
    background-color: #4CAF50; /* 绿色 */
}

#startButton:hover {
    background-color: #45a049; /* 深绿色 */
}

.instructions {
    margin-top: 15px;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.instructions p {
    margin-bottom: 5px;
}

.instructions strong {
    color: #333;
}

/* 得分动画样式 */
.points-animation {
    position: absolute;
    z-index: 100;
    font-weight: bold;
    font-size: 20px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    pointer-events: none;
}

/* 食物指南 */
.food-guide {
    margin-top: 15px;
    text-align: left;
    padding: 10px;
    background-color: #f8f8f8;
    border-radius: 5px;
    font-size: 0.85rem;
    display: none; /* 默认隐藏，可以添加一个按钮来显示它 */
    border: 1px solid #ddd;
}

.food-guide h3 {
    margin-bottom: 10px;
    color: #333;
    text-align: center;
}

.food-guide-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.food-color {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    margin-right: 8px;
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

.food-guide-note {
    margin-top: 10px;
    font-style: italic;
    color: #666;
    border-top: 1px dashed #ccc;
    padding-top: 8px;
}

#toggleGuideButton {
    background-color: #2196F3;
}

#toggleGuideButton:hover {
    background-color: #0b7dda;
}

@media (max-width: 500px) {
    canvas {
        width: 300px;
        height: 300px;
    }
    
    .food-guide-item {
        margin-bottom: 5px;
    }
}